from flask import Blueprint, request, jsonify, session
from database.mongo_connect import db
from bson.objectid import ObjectId
import datetime

admin = Blueprint("admin", __name__)

def require_admin():
    """Check if user is admin"""
    if "user" not in session:
        return False
    return session["user"].get("role") == "admin"

@admin.route("/all-applications", methods=["GET"])
def view_all():
    """Get all applications (admin only)"""
    if not require_admin():
        return jsonify({"error": "Admin access required"}), 403

    # Get query parameters
    status = request.args.get('status')
    business_type = request.args.get('business_type')
    page = int(request.args.get('page', 1))
    limit = int(request.args.get('limit', 10))

    # Build query
    query = {}
    if status:
        query['status'] = status
    if business_type:
        query['business_type'] = business_type

    # Get applications with pagination
    skip = (page - 1) * limit
    apps = list(db.applications.find(query).sort("created_at", -1).skip(skip).limit(limit))
    total = db.applications.count_documents(query)

    for app in apps:
        app["_id"] = str(app["_id"])

    return jsonify({
        "applications": apps,
        "pagination": {
            "page": page,
            "limit": limit,
            "total": total,
            "pages": (total + limit - 1) // limit
        }
    })

@admin.route("/statistics", methods=["GET"])
def get_statistics():
    """Get application statistics (admin only)"""
    if not require_admin():
        return jsonify({"error": "Admin access required"}), 403

    # Get basic counts
    total_applications = db.applications.count_documents({})
    pending_applications = db.applications.count_documents({"status": "submitted"})
    approved_applications = db.applications.count_documents({"status": "approved"})
    rejected_applications = db.applications.count_documents({"status": "rejected"})

    # Get applications by business type
    business_type_stats = list(db.applications.aggregate([
        {"$group": {"_id": "$business_type", "count": {"$sum": 1}}}
    ]))

    # Get applications by month
    monthly_stats = list(db.applications.aggregate([
        {
            "$group": {
                "_id": {
                    "year": {"$year": "$created_at"},
                    "month": {"$month": "$created_at"}
                },
                "count": {"$sum": 1}
            }
        },
        {"$sort": {"_id.year": 1, "_id.month": 1}}
    ]))

    return jsonify({
        "total_applications": total_applications,
        "pending_applications": pending_applications,
        "approved_applications": approved_applications,
        "rejected_applications": rejected_applications,
        "business_type_distribution": business_type_stats,
        "monthly_applications": monthly_stats
    })

@admin.route("/application/<application_id>/approve", methods=["POST"])
def approve_application(application_id):
    """Approve an application (admin only)"""
    if not require_admin():
        return jsonify({"error": "Admin access required"}), 403

    data = request.json or {}
    comments = data.get("comments", "")

    try:
        result = db.applications.update_one(
            {"_id": ObjectId(application_id)},
            {"$set": {
                "status": "approved",
                "approved_at": datetime.datetime.now(),
                "approved_by": session["user"]["username"],
                "admin_comments": comments,
                "updated_at": datetime.datetime.now()
            }}
        )

        if result.modified_count > 0:
            return jsonify({"message": "Application approved successfully"})
        else:
            return jsonify({"error": "Application not found"}), 404
    except:
        return jsonify({"error": "Invalid application ID"}), 400

@admin.route("/application/<application_id>/reject", methods=["POST"])
def reject_application(application_id):
    """Reject an application (admin only)"""
    if not require_admin():
        return jsonify({"error": "Admin access required"}), 403

    data = request.json or {}
    comments = data.get("comments", "")

    if not comments:
        return jsonify({"error": "Comments required for rejection"}), 400

    try:
        result = db.applications.update_one(
            {"_id": ObjectId(application_id)},
            {"$set": {
                "status": "rejected",
                "rejected_at": datetime.datetime.now(),
                "rejected_by": session["user"]["username"],
                "admin_comments": comments,
                "updated_at": datetime.datetime.now()
            }}
        )

        if result.modified_count > 0:
            return jsonify({"message": "Application rejected"})
        else:
            return jsonify({"error": "Application not found"}), 404
    except:
        return jsonify({"error": "Invalid application ID"}), 400

@admin.route("/users", methods=["GET"])
def get_users():
    """Get all users (admin only)"""
    if not require_admin():
        return jsonify({"error": "Admin access required"}), 403

    users = list(db.users.find({}, {"password": 0}))  # Exclude passwords

    for user in users:
        user["_id"] = str(user["_id"])

    return jsonify(users)

@admin.route("/agencies/status", methods=["GET"])
def get_agency_status():
    """Get status of all agency applications"""
    if not require_admin():
        return jsonify({"error": "Admin access required"}), 403

    agency_apps = list(db.agency_applications.find())

    for app in agency_apps:
        app["_id"] = str(app["_id"])

    return jsonify(agency_apps)
