{"info": {"name": "StartSmart AI Analysis Test", "_postman_id": "b29e0be2-1a75-489b-8b0c-e1a7280ed7f3", "description": "Test the /ai endpoint of StartSmart Flask API", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Analyze Business (AI)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"business_type\": \"retail\",\n  \"location\": \"Chennai\",\n  \"description\": \"A mobile shop that also repairs gadgets\"\n}"}, "url": {"raw": "http://127.0.0.1:5000/ai/", "protocol": "http", "host": ["127.0.0.1"], "port": "5000", "path": ["ai", ""]}}, "response": []}]}