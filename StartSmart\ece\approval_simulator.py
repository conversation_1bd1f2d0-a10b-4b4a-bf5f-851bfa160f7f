import tkinter as tk
from tkinter import ttk, messagebox
import requests
import threading
import time
import json
from datetime import datetime

class ApprovalStatusMonitor:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("StartSmart ECE - Approval Status Monitor")
        self.root.geometry("800x600")
        self.root.configure(bg="#2c3e50")

        # API base URL
        self.api_base = "http://127.0.0.1:5000/api"

        # Current status
        self.current_status = "disconnected"
        self.applications = []
        self.selected_app_id = None

        # Create GUI
        self.create_widgets()

        # Start monitoring
        self.monitoring = True
        self.start_monitoring()

    def create_widgets(self):
        # Title
        title_frame = tk.Frame(self.root, bg="#2c3e50")
        title_frame.pack(pady=10)

        title_label = tk.Label(title_frame, text="StartSmart ECE - Approval Status Monitor",
                              font=("Arial", 18, "bold"), fg="white", bg="#2c3e50")
        title_label.pack()

        subtitle_label = tk.Label(title_frame, text="Real-time Hardware Simulation for Government Approval Tracking",
                                 font=("Arial", 10), fg="#bdc3c7", bg="#2c3e50")
        subtitle_label.pack()

        # Main container
        main_frame = tk.Frame(self.root, bg="#2c3e50")
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

        # Left panel - Status Display
        left_frame = tk.Frame(main_frame, bg="#34495e", relief=tk.RAISED, bd=2)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))

        # Status indicator
        status_frame = tk.Frame(left_frame, bg="#34495e")
        status_frame.pack(pady=20)

        tk.Label(status_frame, text="System Status", font=("Arial", 14, "bold"),
                fg="white", bg="#34495e").pack()

        # LED simulation canvas
        self.canvas = tk.Canvas(status_frame, width=150, height=150, bg="#2c3e50", highlightthickness=0)
        self.canvas.pack(pady=10)

        # Create LED circle
        self.led_circle = self.canvas.create_oval(25, 25, 125, 125, fill="gray", outline="white", width=3)
        self.led_text = self.canvas.create_text(75, 75, text="OFF", font=("Arial", 12, "bold"), fill="white")

        # Status text
        self.status_label = tk.Label(status_frame, text="Disconnected", font=("Arial", 12),
                                    fg="#e74c3c", bg="#34495e")
        self.status_label.pack()

        # Connection info
        connection_frame = tk.Frame(left_frame, bg="#34495e")
        connection_frame.pack(pady=10, padx=10, fill=tk.X)

        tk.Label(connection_frame, text="API Connection:", font=("Arial", 10, "bold"),
                fg="white", bg="#34495e").pack(anchor=tk.W)

        self.connection_label = tk.Label(connection_frame, text="Checking...", font=("Arial", 9),
                                        fg="#f39c12", bg="#34495e")
        self.connection_label.pack(anchor=tk.W)

        # Right panel - Application List
        right_frame = tk.Frame(main_frame, bg="#34495e", relief=tk.RAISED, bd=2)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)

        # Applications header
        app_header = tk.Frame(right_frame, bg="#34495e")
        app_header.pack(fill=tk.X, padx=10, pady=10)

        tk.Label(app_header, text="Applications Monitor", font=("Arial", 14, "bold"),
                fg="white", bg="#34495e").pack()

        # Refresh button
        refresh_btn = tk.Button(app_header, text="Refresh", command=self.refresh_applications,
                               bg="#3498db", fg="white", font=("Arial", 10), relief=tk.FLAT)
        refresh_btn.pack(pady=5)

        # Applications listbox with scrollbar
        list_frame = tk.Frame(right_frame, bg="#34495e")
        list_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        scrollbar = tk.Scrollbar(list_frame)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        self.app_listbox = tk.Listbox(list_frame, yscrollcommand=scrollbar.set,
                                     bg="#2c3e50", fg="white", font=("Arial", 10),
                                     selectbackground="#3498db")
        self.app_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        self.app_listbox.bind('<<ListboxSelect>>', self.on_app_select)

        scrollbar.config(command=self.app_listbox.yview)

        # Selected application details
        details_frame = tk.Frame(right_frame, bg="#34495e")
        details_frame.pack(fill=tk.X, padx=10, pady=10)

        tk.Label(details_frame, text="Selected Application Details:", font=("Arial", 11, "bold"),
                fg="white", bg="#34495e").pack(anchor=tk.W)

        self.details_text = tk.Text(details_frame, height=8, bg="#2c3e50", fg="white",
                                   font=("Arial", 9), wrap=tk.WORD)
        self.details_text.pack(fill=tk.X, pady=5)

        # Control buttons
        control_frame = tk.Frame(self.root, bg="#2c3e50")
        control_frame.pack(fill=tk.X, padx=20, pady=10)

        # Simulate status changes (for demo)
        simulate_frame = tk.Frame(control_frame, bg="#2c3e50")
        simulate_frame.pack(side=tk.LEFT)

        tk.Label(simulate_frame, text="Simulate Status:", font=("Arial", 10, "bold"),
                fg="white", bg="#2c3e50").pack(side=tk.LEFT, padx=5)

        tk.Button(simulate_frame, text="Pending", command=lambda: self.simulate_status("submitted"),
                 bg="#f39c12", fg="white", relief=tk.FLAT).pack(side=tk.LEFT, padx=2)

        tk.Button(simulate_frame, text="Approved", command=lambda: self.simulate_status("approved"),
                 bg="#27ae60", fg="white", relief=tk.FLAT).pack(side=tk.LEFT, padx=2)

        tk.Button(simulate_frame, text="Rejected", command=lambda: self.simulate_status("rejected"),
                 bg="#e74c3c", fg="white", relief=tk.FLAT).pack(side=tk.LEFT, padx=2)

        # Exit button
        tk.Button(control_frame, text="Exit", command=self.on_closing,
                 bg="#95a5a6", fg="white", relief=tk.FLAT).pack(side=tk.RIGHT, padx=5)

    def update_led_status(self, status):
        """Update LED indicator based on status"""
        self.current_status = status

        if status == "approved":
            self.canvas.itemconfig(self.led_circle, fill="#27ae60")  # Green
            self.canvas.itemconfig(self.led_text, text="APPROVED")
            self.status_label.config(text="Approved", fg="#27ae60")
        elif status == "submitted" or status == "pending":
            self.canvas.itemconfig(self.led_circle, fill="#f39c12")  # Yellow
            self.canvas.itemconfig(self.led_text, text="PENDING")
            self.status_label.config(text="Pending", fg="#f39c12")
        elif status == "rejected":
            self.canvas.itemconfig(self.led_circle, fill="#e74c3c")  # Red
            self.canvas.itemconfig(self.led_text, text="REJECTED")
            self.status_label.config(text="Rejected", fg="#e74c3c")
        elif status == "connected":
            self.canvas.itemconfig(self.led_circle, fill="#3498db")  # Blue
            self.canvas.itemconfig(self.led_text, text="ONLINE")
            self.status_label.config(text="Connected", fg="#3498db")
        else:
            self.canvas.itemconfig(self.led_circle, fill="gray")
            self.canvas.itemconfig(self.led_text, text="OFF")
            self.status_label.config(text="Disconnected", fg="#e74c3c")

    def check_api_connection(self):
        """Check if API is accessible"""
        try:
            response = requests.get(f"{self.api_base}/agencies/list", timeout=5)
            if response.status_code == 200:
                self.connection_label.config(text="✓ Connected to StartSmart API", fg="#27ae60")
                return True
            else:
                self.connection_label.config(text="✗ API Error", fg="#e74c3c")
                return False
        except requests.exceptions.RequestException:
            self.connection_label.config(text="✗ Cannot connect to API", fg="#e74c3c")
            return False

    def fetch_applications(self):
        """Fetch applications from API"""
        try:
            response = requests.get(f"{self.api_base}/applications/", timeout=5)
            if response.status_code == 200:
                self.applications = response.json()
                return True
            else:
                return False
        except requests.exceptions.RequestException:
            return False

    def refresh_applications(self):
        """Refresh the applications list"""
        if self.fetch_applications():
            self.app_listbox.delete(0, tk.END)
            for app in self.applications:
                display_text = f"{app['business_name']} - {app['status'].upper()}"
                self.app_listbox.insert(tk.END, display_text)

    def on_app_select(self, event):
        """Handle application selection"""
        selection = self.app_listbox.curselection()
        if selection:
            index = selection[0]
            app = self.applications[index]
            self.selected_app_id = app['_id']

            # Update LED based on selected application status
            self.update_led_status(app['status'])

            # Show application details
            details = f"""Business Name: {app['business_name']}
Application #: {app.get('application_number', 'N/A')}
Business Type: {app['business_type']}
Location: {app['location']}
Applicant: {app['applicant_name']}
Status: {app['status'].upper()}
Created: {app.get('created_at', 'N/A')}
Email: {app['contact_email']}

Description:
{app['description']}"""

            self.details_text.delete(1.0, tk.END)
            self.details_text.insert(1.0, details)

    def simulate_status(self, status):
        """Simulate status change for demo purposes"""
        if self.selected_app_id:
            try:
                # Update status via API (this would normally be done by admin)
                # For simulation, we'll just update the LED
                self.update_led_status(status)
                messagebox.showinfo("Status Simulation",
                                   f"Simulated status change to: {status.upper()}")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to simulate status: {str(e)}")
        else:
            messagebox.showwarning("No Selection", "Please select an application first")

    def monitor_loop(self):
        """Main monitoring loop"""
        while self.monitoring:
            try:
                # Check API connection
                if self.check_api_connection():
                    if not self.applications:
                        self.update_led_status("connected")

                    # Refresh applications every 10 seconds
                    self.refresh_applications()
                else:
                    self.update_led_status("disconnected")

                time.sleep(10)  # Check every 10 seconds

            except Exception as e:
                print(f"Monitoring error: {e}")
                time.sleep(5)

    def start_monitoring(self):
        """Start the monitoring thread"""
        monitor_thread = threading.Thread(target=self.monitor_loop, daemon=True)
        monitor_thread.start()

        # Initial load
        self.root.after(1000, self.refresh_applications)

    def on_closing(self):
        """Handle window closing"""
        self.monitoring = False
        self.root.destroy()

    def run(self):
        """Start the GUI"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.mainloop()

if __name__ == "__main__":
    print("Starting StartSmart ECE Approval Status Monitor...")
    print("This simulates hardware LED indicators for approval status tracking")
    print("Make sure the StartSmart Flask application is running on http://127.0.0.1:5000")

    monitor = ApprovalStatusMonitor()
    monitor.run()
