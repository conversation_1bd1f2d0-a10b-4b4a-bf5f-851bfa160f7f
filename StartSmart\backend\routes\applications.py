from flask import Blueprint, request, jsonify
from database.mongo_connect import db
import datetime
from bson.objectid import ObjectId

applications = Blueprint("applications", __name__)

@applications.route("/", methods=["POST"])
def create_application():
    """Create a new application"""
    data = request.json

    # Validate required fields
    required_fields = ['business_name', 'business_type', 'location', 'description', 'applicant_name', 'contact_email']
    for field in required_fields:
        if field not in data:
            return jsonify({"error": f"Missing required field: {field}"}), 400

    # Add metadata
    application = {
        **data,
        "status": "submitted",
        "created_at": datetime.datetime.utcnow(),
        "updated_at": datetime.datetime.utcnow(),
        "application_number": f"APP{datetime.datetime.now().strftime('%Y%m%d%H%M%S')}",
        "progress": {
            "documents_uploaded": False,
            "ai_analysis_completed": False,
            "agency_submissions": {},
            "approvals_received": {}
        }
    }

    result = db.applications.insert_one(application)
    application["_id"] = str(result.inserted_id)

    return jsonify({
        "message": "Application submitted successfully",
        "application": application
    })

@applications.route("/", methods=["GET"])
def get_applications():
    """Get all applications for the current user"""
    # In a real app, you'd filter by user_id from session/token
    applications_list = list(db.applications.find().sort("created_at", -1))

    for app in applications_list:
        app["_id"] = str(app["_id"])

    return jsonify(applications_list)

@applications.route("/<application_id>", methods=["GET"])
def get_application(application_id):
    """Get a specific application"""
    try:
        application = db.applications.find_one({"_id": ObjectId(application_id)})
        if application:
            application["_id"] = str(application["_id"])
            return jsonify(application)
        else:
            return jsonify({"error": "Application not found"}), 404
    except:
        return jsonify({"error": "Invalid application ID"}), 400

@applications.route("/<application_id>", methods=["PUT"])
def update_application(application_id):
    """Update an application"""
    try:
        data = request.json
        data["updated_at"] = datetime.datetime.utcnow()

        result = db.applications.update_one(
            {"_id": ObjectId(application_id)},
            {"$set": data}
        )

        if result.modified_count > 0:
            return jsonify({"message": "Application updated successfully"})
        else:
            return jsonify({"error": "Application not found"}), 404
    except:
        return jsonify({"error": "Invalid application ID"}), 400

@applications.route("/<application_id>/status", methods=["PUT"])
def update_application_status(application_id):
    """Update application status"""
    try:
        data = request.json

        if "status" not in data:
            return jsonify({"error": "Missing status field"}), 400

        update_data = {
            "status": data["status"],
            "updated_at": datetime.datetime.utcnow()
        }

        if "comments" in data:
            update_data["status_comments"] = data["comments"]

        result = db.applications.update_one(
            {"_id": ObjectId(application_id)},
            {"$set": update_data}
        )

        if result.modified_count > 0:
            return jsonify({"message": "Status updated successfully"})
        else:
            return jsonify({"error": "Application not found"}), 404
    except:
        return jsonify({"error": "Invalid application ID"}), 400

@applications.route("/<application_id>/progress", methods=["PUT"])
def update_progress(application_id):
    """Update application progress"""
    try:
        data = request.json

        result = db.applications.update_one(
            {"_id": ObjectId(application_id)},
            {"$set": {
                "progress": data,
                "updated_at": datetime.datetime.utcnow()
            }}
        )

        if result.modified_count > 0:
            return jsonify({"message": "Progress updated successfully"})
        else:
            return jsonify({"error": "Application not found"}), 404
    except:
        return jsonify({"error": "Invalid application ID"}), 400
