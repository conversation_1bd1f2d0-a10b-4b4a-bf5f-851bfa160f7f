from flask import Blueprint, request, jsonify
from werkzeug.utils import secure_filename
from database.mongo_connect import db
import os
import datetime
import base64

documents = Blueprint("documents", __name__)

# Allowed file extensions
ALLOWED_EXTENSIONS = {'txt', 'pdf', 'png', 'jpg', 'jpeg', 'gif', 'doc', 'docx'}

def allowed_file(filename):
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

@documents.route("/upload", methods=["POST"])
def upload_document():
    """Upload a document"""
    try:
        # Check if file is in request
        if 'file' not in request.files:
            return jsonify({"error": "No file provided"}), 400
        
        file = request.files['file']
        application_id = request.form.get('application_id')
        document_type = request.form.get('document_type')
        
        if not application_id or not document_type:
            return jsonify({"error": "Missing application_id or document_type"}), 400
        
        if file.filename == '':
            return jsonify({"error": "No file selected"}), 400
        
        if file and allowed_file(file.filename):
            filename = secure_filename(file.filename)
            
            # Create uploads directory if it doesn't exist
            upload_dir = os.path.join(os.path.dirname(__file__), '../../uploads')
            os.makedirs(upload_dir, exist_ok=True)
            
            # Save file
            file_path = os.path.join(upload_dir, filename)
            file.save(file_path)
            
            # Store document info in database
            document_info = {
                "application_id": application_id,
                "document_type": document_type,
                "filename": filename,
                "file_path": file_path,
                "uploaded_at": datetime.datetime.utcnow(),
                "status": "uploaded",
                "verified": False
            }
            
            result = db.documents.insert_one(document_info)
            document_info["_id"] = str(result.inserted_id)
            
            return jsonify({
                "message": "Document uploaded successfully",
                "document": document_info
            })
        else:
            return jsonify({"error": "File type not allowed"}), 400
            
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@documents.route("/list/<application_id>", methods=["GET"])
def get_documents(application_id):
    """Get all documents for an application"""
    documents_list = list(db.documents.find({"application_id": application_id}))
    
    for doc in documents_list:
        doc["_id"] = str(doc["_id"])
        # Remove file_path for security
        doc.pop("file_path", None)
    
    return jsonify(documents_list)

@documents.route("/verify", methods=["POST"])
def verify_document():
    """Verify a document (AI-powered simulation)"""
    data = request.json
    
    if "document_id" not in data:
        return jsonify({"error": "Missing document_id"}), 400
    
    # Simulate AI verification
    verification_result = {
        "verified": True,
        "confidence": 0.95,
        "issues": [],
        "verified_at": datetime.datetime.utcnow()
    }
    
    # Update document in database
    db.documents.update_one(
        {"_id": data["document_id"]},
        {"$set": {
            "verified": verification_result["verified"],
            "verification_confidence": verification_result["confidence"],
            "verification_issues": verification_result["issues"],
            "verified_at": verification_result["verified_at"]
        }}
    )
    
    return jsonify({
        "message": "Document verification completed",
        "verification": verification_result
    })

@documents.route("/required/<business_type>", methods=["GET"])
def get_required_documents(business_type):
    """Get required documents based on business type"""
    document_requirements = {
        "retail": [
            "Business Plan",
            "Identity Proof (Aadhar/PAN)",
            "Address Proof",
            "Bank Statement",
            "Rent Agreement"
        ],
        "manufacturing": [
            "Business Plan",
            "Identity Proof (Aadhar/PAN)",
            "Address Proof",
            "Bank Statement",
            "Environmental Impact Assessment",
            "Fire Safety Plan",
            "Machinery Details"
        ],
        "restaurant": [
            "Business Plan",
            "Identity Proof (Aadhar/PAN)",
            "Address Proof",
            "Bank Statement",
            "Food Safety License Application",
            "Kitchen Layout Plan"
        ],
        "tech": [
            "Business Plan",
            "Identity Proof (Aadhar/PAN)",
            "Address Proof",
            "Bank Statement",
            "Office Lease Agreement",
            "Technology Stack Details"
        ],
        "healthcare": [
            "Business Plan",
            "Identity Proof (Aadhar/PAN)",
            "Address Proof",
            "Bank Statement",
            "Medical Equipment List",
            "Doctor Credentials",
            "Waste Management Plan"
        ],
        "education": [
            "Business Plan",
            "Identity Proof (Aadhar/PAN)",
            "Address Proof",
            "Bank Statement",
            "Faculty Details",
            "Infrastructure Plan",
            "Curriculum Details"
        ]
    }
    
    required_docs = document_requirements.get(business_type.lower(), document_requirements["retail"])
    
    return jsonify({
        "business_type": business_type,
        "required_documents": required_docs
    })

@documents.route("/template/<document_type>", methods=["GET"])
def get_document_template(document_type):
    """Get template for a specific document type"""
    templates = {
        "business_plan": {
            "name": "Business Plan Template",
            "sections": [
                "Executive Summary",
                "Business Description",
                "Market Analysis",
                "Financial Projections",
                "Management Team"
            ],
            "format": "PDF/DOC",
            "max_pages": 20
        },
        "identity_proof": {
            "name": "Identity Proof",
            "accepted_documents": [
                "Aadhar Card",
                "PAN Card",
                "Passport",
                "Driving License"
            ],
            "format": "PDF/JPG",
            "requirements": ["Clear scan", "All corners visible"]
        },
        "address_proof": {
            "name": "Address Proof",
            "accepted_documents": [
                "Utility Bill",
                "Bank Statement",
                "Rent Agreement",
                "Property Tax Receipt"
            ],
            "format": "PDF/JPG",
            "requirements": ["Recent (within 3 months)", "Clear address visible"]
        }
    }
    
    template = templates.get(document_type.lower())
    if template:
        return jsonify(template)
    else:
        return jsonify({"error": "Template not found"}), 404
