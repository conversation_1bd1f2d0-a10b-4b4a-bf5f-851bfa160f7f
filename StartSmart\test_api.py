import requests
import json

# Test the AI analysis endpoint
def test_ai_analysis():
    url = "http://127.0.0.1:5000/api/ai/"
    data = {
        "business_type": "retail",
        "location": "mumbai", 
        "description": "A mobile phone retail store selling smartphones and accessories"
    }
    
    try:
        response = requests.post(url, json=data)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
    except Exception as e:
        print(f"Error: {e}")

# Test agencies endpoint
def test_agencies():
    url = "http://127.0.0.1:5000/api/agencies/list"
    
    try:
        response = requests.get(url)
        print(f"Status Code: {response.status_code}")
        agencies = response.json()
        print(f"Number of agencies: {len(agencies)}")
        for key, agency in agencies.items():
            print(f"- {agency['name']}: {agency['description']}")
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    print("Testing StartSmart API endpoints...")
    print("\n1. Testing AI Analysis:")
    test_ai_analysis()
    
    print("\n2. Testing Agencies:")
    test_agencies()
