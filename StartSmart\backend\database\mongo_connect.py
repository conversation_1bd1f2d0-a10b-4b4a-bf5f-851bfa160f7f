from pymongo import MongoClient
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Get MongoDB URI from environment or use default
MONGO_URI = os.getenv("MONGO_URI", "mongodb://localhost:27017/startsmart_db")

try:
    client = MongoClient(MONGO_URI)
    # Test the connection
    client.admin.command('ping')
    db = client["startsmart_db"]
    print("Connected to MongoDB successfully")
except Exception as e:
    print(f"Failed to connect to MongoDB: {e}")
    # Fallback to local MongoDB
    client = MongoClient("mongodb://localhost:27017/startsmart_db")
    db = client["startsmart_db"]
