from flask import Blueprint, request, jsonify, session
from database.mongo_connect import db
import bcrypt
import datetime
from bson.objectid import ObjectId

auth = Blueprint("auth", __name__)

# Default users for demo
default_users = {
    "admin": {
        "username": "admin",
        "password": "admin123",
        "role": "admin",
        "email": "<EMAIL>",
        "full_name": "System Administrator"
    },
    "entrepreneur": {
        "username": "entrepreneur",
        "password": "user123",
        "role": "user",
        "email": "<EMAIL>",
        "full_name": "John Entrepreneur"
    }
}

def hash_password(password):
    """Hash a password"""
    return bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt())

def check_password(password, hashed):
    """Check if password matches hash"""
    return bcrypt.checkpw(password.encode('utf-8'), hashed)

@auth.route("/register", methods=["POST"])
def register():
    """Register a new user"""
    data = request.json

    required_fields = ['username', 'password', 'email', 'full_name']
    for field in required_fields:
        if field not in data:
            return jsonify({"error": f"Missing required field: {field}"}), 400

    # Check if user already exists
    existing_user = db.users.find_one({"$or": [
        {"username": data["username"]},
        {"email": data["email"]}
    ]})

    if existing_user:
        return jsonify({"error": "User already exists"}), 409

    # Create new user
    user = {
        "username": data["username"],
        "password": hash_password(data["password"]),
        "email": data["email"],
        "full_name": data["full_name"],
        "role": data.get("role", "user"),
        "created_at": datetime.datetime.utcnow(),
        "last_login": None,
        "is_active": True
    }

    result = db.users.insert_one(user)
    user["_id"] = str(result.inserted_id)
    user.pop("password")  # Don't return password

    return jsonify({
        "message": "User registered successfully",
        "user": user
    })

@auth.route("/login", methods=["POST"])
def login():
    """User login"""
    data = request.json

    if not data.get("username") or not data.get("password"):
        return jsonify({"error": "Username and password required"}), 400

    # Check default users first (for demo)
    if data["username"] in default_users:
        default_user = default_users[data["username"]]
        if default_user["password"] == data["password"]:
            session["user"] = {
                "username": default_user["username"],
                "role": default_user["role"],
                "email": default_user["email"],
                "full_name": default_user["full_name"]
            }
            return jsonify({
                "message": "Login successful",
                "user": session["user"]
            })

    # Check database users
    user = db.users.find_one({"username": data["username"]})

    if user and check_password(data["password"], user["password"]):
        # Update last login
        db.users.update_one(
            {"_id": user["_id"]},
            {"$set": {"last_login": datetime.datetime.utcnow()}}
        )

        session["user"] = {
            "id": str(user["_id"]),
            "username": user["username"],
            "role": user["role"],
            "email": user["email"],
            "full_name": user["full_name"]
        }

        return jsonify({
            "message": "Login successful",
            "user": session["user"]
        })

    return jsonify({"error": "Invalid credentials"}), 401

@auth.route("/logout", methods=["POST"])
def logout():
    """User logout"""
    session.pop("user", None)
    return jsonify({"message": "Logged out successfully"})

@auth.route("/profile", methods=["GET"])
def get_profile():
    """Get current user profile"""
    if "user" not in session:
        return jsonify({"error": "Not authenticated"}), 401

    return jsonify(session["user"])

@auth.route("/profile", methods=["PUT"])
def update_profile():
    """Update user profile"""
    if "user" not in session:
        return jsonify({"error": "Not authenticated"}), 401

    data = request.json
    user_id = session["user"].get("id")

    if not user_id:
        return jsonify({"error": "Invalid session"}), 401

    # Update allowed fields
    allowed_fields = ["full_name", "email"]
    update_data = {k: v for k, v in data.items() if k in allowed_fields}
    update_data["updated_at"] = datetime.datetime.utcnow()

    try:
        result = db.users.update_one(
            {"_id": ObjectId(user_id)},
            {"$set": update_data}
        )

        if result.modified_count > 0:
            # Update session
            for key, value in update_data.items():
                if key in session["user"]:
                    session["user"][key] = value

            return jsonify({"message": "Profile updated successfully"})
        else:
            return jsonify({"error": "User not found"}), 404
    except:
        return jsonify({"error": "Invalid user ID"}), 400
