from flask import Blueprint, request, jsonify
from database.mongo_connect import db
import datetime

agencies = Blueprint("agencies", __name__)

# Government agencies data
GOVERNMENT_AGENCIES = {
    "trade_license": {
        "name": "Trade License Department",
        "description": "Issues trade licenses for commercial activities",
        "required_documents": ["Business Plan", "Identity Proof", "Address Proof"],
        "processing_time": "7-14 days",
        "fees": 5000,
        "requirements": ["Valid business address", "Clean criminal record"]
    },
    "gst_registration": {
        "name": "GST Registration Office",
        "description": "Goods and Services Tax registration",
        "required_documents": ["PAN Card", "Address Proof", "Bank Statement"],
        "processing_time": "3-7 days",
        "fees": 0,
        "requirements": ["Annual turnover > 20 lakhs", "Valid PAN"]
    },
    "fire_safety": {
        "name": "Fire Safety Department",
        "description": "Fire safety clearance for commercial establishments",
        "required_documents": ["Building Plan", "Fire Safety Equipment List"],
        "processing_time": "10-15 days",
        "fees": 3000,
        "requirements": ["Fire safety equipment", "Emergency exits"]
    },
    "pollution_control": {
        "name": "Pollution Control Board",
        "description": "Environmental clearance for businesses",
        "required_documents": ["Environmental Impact Assessment", "Waste Management Plan"],
        "processing_time": "15-30 days",
        "fees": 10000,
        "requirements": ["Environmental compliance", "Waste management system"]
    },
    "labor_license": {
        "name": "Labor Department",
        "description": "License for employing workers",
        "required_documents": ["Employee List", "Salary Structure", "Working Conditions Report"],
        "processing_time": "5-10 days",
        "fees": 2000,
        "requirements": ["Minimum wage compliance", "Safe working conditions"]
    },
    "shop_establishment": {
        "name": "Shop & Establishment Department",
        "description": "Registration under Shop & Establishment Act",
        "required_documents": ["Rent Agreement", "Identity Proof", "Business Details"],
        "processing_time": "3-5 days",
        "fees": 1500,
        "requirements": ["Valid business premises", "Compliance with working hours"]
    }
}

@agencies.route("/list", methods=["GET"])
def get_agencies():
    """Get list of all government agencies"""
    return jsonify(GOVERNMENT_AGENCIES)

@agencies.route("/<agency_id>", methods=["GET"])
def get_agency_details(agency_id):
    """Get details of a specific agency"""
    if agency_id in GOVERNMENT_AGENCIES:
        return jsonify(GOVERNMENT_AGENCIES[agency_id])
    return jsonify({"error": "Agency not found"}), 404

@agencies.route("/requirements/<business_type>", methods=["GET"])
def get_requirements_by_business(business_type):
    """Get required agencies based on business type"""
    business_requirements = {
        "retail": ["trade_license", "gst_registration", "shop_establishment"],
        "manufacturing": ["trade_license", "gst_registration", "fire_safety", "pollution_control", "labor_license"],
        "restaurant": ["trade_license", "gst_registration", "fire_safety", "shop_establishment"],
        "tech": ["trade_license", "gst_registration", "shop_establishment"],
        "healthcare": ["trade_license", "gst_registration", "fire_safety", "pollution_control"],
        "education": ["trade_license", "fire_safety", "shop_establishment"]
    }
    
    required_agencies = business_requirements.get(business_type.lower(), ["trade_license", "gst_registration"])
    
    result = {}
    for agency_id in required_agencies:
        if agency_id in GOVERNMENT_AGENCIES:
            result[agency_id] = GOVERNMENT_AGENCIES[agency_id]
    
    return jsonify(result)

@agencies.route("/submit-application", methods=["POST"])
def submit_agency_application():
    """Submit application to a specific agency"""
    data = request.json
    
    required_fields = ['agency_id', 'application_id', 'documents']
    for field in required_fields:
        if field not in data:
            return jsonify({"error": f"Missing required field: {field}"}), 400
    
    # Create agency application record
    agency_application = {
        "agency_id": data["agency_id"],
        "application_id": data["application_id"],
        "documents": data["documents"],
        "status": "submitted",
        "submitted_at": datetime.datetime.utcnow(),
        "estimated_completion": datetime.datetime.utcnow() + datetime.timedelta(days=7),
        "comments": []
    }
    
    # Insert into database
    result = db.agency_applications.insert_one(agency_application)
    agency_application["_id"] = str(result.inserted_id)
    
    return jsonify({
        "message": "Application submitted successfully",
        "agency_application": agency_application
    })

@agencies.route("/status/<application_id>", methods=["GET"])
def get_application_status(application_id):
    """Get status of applications for a specific main application"""
    agency_apps = list(db.agency_applications.find({"application_id": application_id}))
    
    for app in agency_apps:
        app["_id"] = str(app["_id"])
    
    return jsonify(agency_apps)

@agencies.route("/update-status", methods=["POST"])
def update_application_status():
    """Update status of an agency application (for simulation)"""
    data = request.json
    
    required_fields = ['agency_application_id', 'status']
    for field in required_fields:
        if field not in data:
            return jsonify({"error": f"Missing required field: {field}"}), 400
    
    # Update status
    update_data = {
        "status": data["status"],
        "updated_at": datetime.datetime.utcnow()
    }
    
    if "comments" in data:
        update_data["$push"] = {"comments": {
            "comment": data["comments"],
            "timestamp": datetime.datetime.utcnow()
        }}
    
    result = db.agency_applications.update_one(
        {"_id": data["agency_application_id"]},
        {"$set": update_data}
    )
    
    if result.modified_count > 0:
        return jsonify({"message": "Status updated successfully"})
    else:
        return jsonify({"error": "Application not found"}), 404
