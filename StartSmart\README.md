# StartSmart

## Overview
StartSmart is an AI-powered approval and documentation platform that simplifies the process for entrepreneurs to obtain government approvals.

## Features
- User login/logout
- Submit new applications
- AI analysis of business for approval suggestions
- Admin view for all applications
- ECE simulation of approval status using Tkinter

## Setup
1. Install dependencies:
```
pip install -r requirements.txt
```

2. Create a `.env` file:
```
MONGO_URI=mongodb+srv://<username>:<password>@cluster.mongodb.net/startsmart_db
OPENAI_API_KEY=sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
```

3. Run the app:
```
python backend/app.py
```

4. Visit `http://127.0.0.1:5000`

## Simulated ECE Contribution
Run `approval_simulator.py` inside the `ece/` folder to simulate a hardware LED display showing approval status using Python GUI.
