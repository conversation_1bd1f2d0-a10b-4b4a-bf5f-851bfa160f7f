#!/usr/bin/env python3
"""
StartSmart ECE Demo Script
Demonstrates the ECE integration without GUI for console output
"""

import requests
import time
import json
from datetime import datetime

def print_header():
    print("=" * 80)
    print("🎓 StartSmart ECE Integration Demo")
    print("=" * 80)
    print("This demo shows how ECE hardware components integrate with StartSmart")
    print("Simulating: LEDs, Displays, Sensors, and IoT Communication")
    print("=" * 80)
    print()

def simulate_led_display(led_states):
    """Display LED states in console"""
    print("🔌 Hardware LED Status:")
    print("-" * 40)
    
    led_symbols = {
        "on": "🟢",
        "off": "🔴", 
        "blinking": "🟡"
    }
    
    for led_name, state in led_states.items():
        symbol = led_symbols.get(state, "⚫")
        print(f"  {led_name.upper():12} {symbol} {state.upper()}")
    
    print("-" * 40)

def simulate_display_message(message):
    """Simulate LCD/OLED display"""
    print(f"📺 DISPLAY: [{message:20}]")

def simulate_sensors():
    """Simulate sensor readings"""
    import random
    sensors = {
        "Temperature": f"{25.0 + random.uniform(-2, 5):.1f}°C",
        "Humidity": f"{60.0 + random.uniform(-10, 15):.1f}%",
        "Light Level": f"{500 + random.uniform(-100, 200):.0f} lux"
    }
    
    print("🌡️  Sensor Readings:")
    for sensor, value in sensors.items():
        print(f"  {sensor:12}: {value}")

def test_api_endpoints():
    """Test all IoT API endpoints"""
    api_base = "http://127.0.0.1:5000/api"
    
    print("🔗 Testing API Endpoints:")
    print("-" * 40)
    
    endpoints = [
        ("/iot/system-health", "System Health"),
        ("/iot/hardware-simulation", "Hardware Simulation"),
        ("/iot/status", "IoT Status"),
        ("/iot/alerts", "System Alerts")
    ]
    
    for endpoint, description in endpoints:
        try:
            response = requests.get(f"{api_base}{endpoint}", timeout=5)
            if response.status_code == 200:
                print(f"  ✅ {description:20}: Connected")
            else:
                print(f"  ❌ {description:20}: Error {response.status_code}")
        except requests.exceptions.RequestException:
            print(f"  ❌ {description:20}: Connection Failed")
    
    print("-" * 40)

def get_hardware_data():
    """Get hardware simulation data from API"""
    try:
        response = requests.get("http://127.0.0.1:5000/api/iot/hardware-simulation", timeout=5)
        if response.status_code == 200:
            return response.json()
        return None
    except requests.exceptions.RequestException:
        return None

def get_system_health():
    """Get system health data"""
    try:
        response = requests.get("http://127.0.0.1:5000/api/iot/system-health", timeout=5)
        if response.status_code == 200:
            return response.json()
        return None
    except requests.exceptions.RequestException:
        return None

def display_system_stats(health_data):
    """Display system statistics"""
    if not health_data:
        return
    
    stats = health_data.get("statistics", {})
    services = health_data.get("services", {})
    
    print("📊 System Statistics:")
    print(f"  Total Applications: {stats.get('total_applications', 0)}")
    print(f"  Pending: {stats.get('pending_applications', 0)}")
    print(f"  Approved: {stats.get('approved_applications', 0)}")
    print(f"  Health Score: {health_data.get('health_score', 0)}%")
    
    print("\n🔧 Service Status:")
    for service, status in services.items():
        symbol = "✅" if status == "online" else "❌"
        print(f"  {symbol} {service.replace('_', ' ').title()}")

def main_demo():
    """Main demo function"""
    print_header()
    
    # Test API connectivity
    test_api_endpoints()
    print()
    
    # Get initial data
    hardware_data = get_hardware_data()
    health_data = get_system_health()
    
    if not hardware_data or not health_data:
        print("❌ Cannot connect to StartSmart API!")
        print("Please ensure the Flask application is running on http://127.0.0.1:5000")
        return
    
    print("✅ Connected to StartSmart API")
    print()
    
    # Display system information
    display_system_stats(health_data)
    print()
    
    # Simulate hardware components
    led_states = hardware_data.get("led_states", {})
    latest_app = hardware_data.get("latest_application", {})
    
    simulate_led_display(led_states)
    print()
    
    # Display current application
    if latest_app.get("name") != "No Applications":
        display_msg = f"{latest_app['name'][:16]} - {latest_app['status'].upper()}"
    else:
        display_msg = "No Applications"
    
    simulate_display_message(display_msg)
    print()
    
    # Show sensor readings
    simulate_sensors()
    print()
    
    # Demonstrate real-time monitoring
    print("🔄 Starting Real-time Monitoring (Press Ctrl+C to stop)...")
    print("=" * 80)
    
    try:
        cycle = 0
        while True:
            cycle += 1
            print(f"\n📡 Monitoring Cycle #{cycle} - {datetime.now().strftime('%H:%M:%S')}")
            
            # Get fresh data
            hardware_data = get_hardware_data()
            if hardware_data:
                led_states = hardware_data.get("led_states", {})
                latest_app = hardware_data.get("latest_application", {})
                system_summary = hardware_data.get("system_summary", {})
                
                # Update display
                if latest_app.get("name") != "No Applications":
                    display_msg = f"{latest_app['name'][:16]} - {latest_app['status'].upper()}"
                else:
                    display_msg = "Waiting for Apps..."
                
                simulate_display_message(display_msg)
                
                # Show key metrics
                print(f"📈 Apps: {system_summary.get('total_applications', 0)} | "
                      f"Pending: {system_summary.get('pending_applications', 0)} | "
                      f"Load: {system_summary.get('system_load', 'normal').upper()}")
                
                # Simulate LED updates (show only changes)
                active_leds = [name for name, state in led_states.items() if state == "on"]
                if active_leds:
                    print(f"🟢 Active LEDs: {', '.join(active_leds).upper()}")
                else:
                    print("🔴 All LEDs: OFF")
            
            time.sleep(10)  # Update every 10 seconds
            
    except KeyboardInterrupt:
        print("\n\n🛑 Monitoring stopped by user")
        print("🔌 Powering down ECE simulation...")
        
        # Simulate shutdown
        shutdown_leds = {led: "off" for led in ["power", "network", "processing", "approval", "error"]}
        simulate_led_display(shutdown_leds)
        simulate_display_message("SHUTDOWN")
        
        print("✅ ECE Demo completed successfully!")

if __name__ == "__main__":
    main_demo()
