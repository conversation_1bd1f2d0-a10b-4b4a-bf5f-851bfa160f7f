"""
StartSmart ECE Hardware Simulation
This script simulates how the system would work with actual hardware components
like LEDs, displays, sensors, and microcontrollers (Arduino/Raspberry Pi)
"""

import requests
import time
import json
from datetime import datetime
import threading

class HardwareSimulator:
    def __init__(self):
        self.api_base = "http://127.0.0.1:5000/api"
        self.device_id = "ECE_DEVICE_001"
        self.running = True
        
        # Simulated hardware states
        self.led_states = {
            "power": False,
            "network": False,
            "processing": False,
            "approval": False,
            "error": False
        }
        
        # Simulated sensors
        self.sensors = {
            "temperature": 25.0,
            "humidity": 60.0,
            "light_level": 500
        }
        
        print("🔧 StartSmart ECE Hardware Simulator Initialized")
        print("📡 Device ID:", self.device_id)
        print("🌐 API Endpoint:", self.api_base)
        print("-" * 50)
    
    def simulate_led(self, led_name, state):
        """Simulate LED control (would control actual GPIO pins in real hardware)"""
        if led_name in self.led_states:
            self.led_states[led_name] = state
            status_symbol = "🟢" if state else "🔴"
            print(f"LED {led_name.upper()}: {status_symbol} {'ON' if state else 'OFF'}")
    
    def simulate_display(self, message):
        """Simulate LCD/OLED display (would use I2C/SPI in real hardware)"""
        print(f"📺 DISPLAY: {message}")
    
    def read_sensors(self):
        """Simulate sensor readings (would read from actual sensors)"""
        # Simulate some variation in sensor readings
        import random
        self.sensors["temperature"] = 25.0 + random.uniform(-2, 5)
        self.sensors["humidity"] = 60.0 + random.uniform(-10, 15)
        self.sensors["light_level"] = 500 + random.uniform(-100, 200)
        
        return self.sensors
    
    def check_api_connection(self):
        """Check connection to StartSmart API"""
        try:
            response = requests.get(f"{self.api_base}/iot/system-health", timeout=5)
            if response.status_code == 200:
                self.simulate_led("network", True)
                return True
            else:
                self.simulate_led("network", False)
                self.simulate_led("error", True)
                return False
        except requests.exceptions.RequestException:
            self.simulate_led("network", False)
            self.simulate_led("error", True)
            return False
    
    def get_hardware_data(self):
        """Fetch hardware-specific data from API"""
        try:
            response = requests.get(f"{self.api_base}/iot/hardware-simulation", timeout=5)
            if response.status_code == 200:
                return response.json()
            return None
        except requests.exceptions.RequestException:
            return None
    
    def update_hardware_status(self, data):
        """Update hardware based on system data"""
        if not data:
            return
        
        led_states = data.get("led_states", {})
        latest_app = data.get("latest_application", {})
        system_summary = data.get("system_summary", {})
        
        # Update LEDs based on system state
        for led_name, state in led_states.items():
            if led_name in self.led_states:
                if state == "on":
                    self.simulate_led(led_name, True)
                elif state == "off":
                    self.simulate_led(led_name, False)
                elif state == "blinking":
                    # In real hardware, this would be a blinking pattern
                    self.simulate_led(led_name, True)
                    print(f"💡 LED {led_name.upper()} is BLINKING")
        
        # Update display with current application info
        if latest_app.get("name") != "No Applications":
            display_msg = f"{latest_app['name'][:16]} - {latest_app['status'].upper()}"
        else:
            display_msg = "No Applications"
        
        self.simulate_display(display_msg)
        
        # Show system summary
        print(f"📊 SYSTEM: {system_summary.get('total_applications', 0)} apps, "
              f"{system_summary.get('pending_applications', 0)} pending")
    
    def send_device_status(self):
        """Send device status to API"""
        try:
            sensor_data = self.read_sensors()
            
            status_data = {
                "device_id": self.device_id,
                "status": "online",
                "metadata": {
                    "led_states": self.led_states,
                    "sensors": sensor_data,
                    "timestamp": datetime.now().isoformat(),
                    "device_type": "ECE_Approval_Monitor",
                    "firmware_version": "1.0.0"
                }
            }
            
            response = requests.post(f"{self.api_base}/iot/device-status", 
                                   json=status_data, timeout=5)
            
            if response.status_code == 200:
                print(f"📤 Device status sent successfully")
            else:
                print(f"❌ Failed to send device status: {response.status_code}")
                
        except requests.exceptions.RequestException as e:
            print(f"❌ Network error sending device status: {e}")
    
    def hardware_loop(self):
        """Main hardware monitoring loop"""
        print("🚀 Starting hardware monitoring loop...")
        
        while self.running:
            try:
                # Power LED always on when running
                self.simulate_led("power", True)
                
                # Check API connection
                if self.check_api_connection():
                    # Get hardware data
                    hardware_data = self.get_hardware_data()
                    
                    if hardware_data:
                        # Update hardware based on system state
                        self.update_hardware_status(hardware_data)
                        
                        # Send device status every 30 seconds
                        if int(time.time()) % 30 == 0:
                            self.send_device_status()
                    
                    self.simulate_led("error", False)
                else:
                    self.simulate_display("API CONNECTION LOST")
                    print("❌ API connection failed")
                
                print("-" * 30)
                time.sleep(5)  # Update every 5 seconds
                
            except KeyboardInterrupt:
                print("\n🛑 Hardware simulation stopped by user")
                break
            except Exception as e:
                print(f"❌ Hardware loop error: {e}")
                self.simulate_led("error", True)
                time.sleep(10)
    
    def start(self):
        """Start the hardware simulation"""
        print("🔌 Powering on ECE hardware simulation...")
        
        # Initialize hardware
        self.simulate_led("power", True)
        self.simulate_display("STARTING UP...")
        
        # Start monitoring in a separate thread
        monitor_thread = threading.Thread(target=self.hardware_loop, daemon=True)
        monitor_thread.start()
        
        try:
            # Keep main thread alive
            while self.running:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n🛑 Shutting down hardware simulation...")
            self.running = False
            
            # Turn off all LEDs
            for led_name in self.led_states:
                self.simulate_led(led_name, False)
            
            self.simulate_display("SHUTDOWN")
            print("✅ Hardware simulation stopped")

def main():
    print("=" * 60)
    print("🎓 StartSmart ECE Hardware Simulation")
    print("=" * 60)
    print("This simulation demonstrates how the StartSmart approval system")
    print("would integrate with actual ECE hardware components:")
    print("• LEDs for status indication")
    print("• LCD/OLED display for information")
    print("• Sensors for environmental monitoring")
    print("• Network communication with main system")
    print("=" * 60)
    print()
    
    # Check if API is available
    try:
        response = requests.get("http://127.0.0.1:5000/api/agencies/list", timeout=5)
        if response.status_code != 200:
            print("❌ StartSmart API is not accessible!")
            print("Please make sure the Flask application is running on http://127.0.0.1:5000")
            return
    except requests.exceptions.RequestException:
        print("❌ Cannot connect to StartSmart API!")
        print("Please make sure the Flask application is running on http://127.0.0.1:5000")
        return
    
    print("✅ API connection verified")
    print("🚀 Starting hardware simulation...\n")
    
    # Start hardware simulation
    simulator = HardwareSimulator()
    simulator.start()

if __name__ == "__main__":
    main()
