import tkinter as tk
from tkinter import ttk
import requests
import threading
import time
import json
from datetime import datetime

class IoTDashboard:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("StartSmart IoT Dashboard - ECE Integration")
        self.root.geometry("1000x700")
        self.root.configure(bg="#1a1a1a")
        
        # API configuration
        self.api_base = "http://127.0.0.1:5000/api"
        
        # Data storage
        self.current_data = {}
        self.monitoring = True
        
        # Create the dashboard
        self.create_dashboard()
        
        # Start data monitoring
        self.start_monitoring()
    
    def create_dashboard(self):
        # Header
        header_frame = tk.Frame(self.root, bg="#2c3e50", height=80)
        header_frame.pack(fill=tk.X, padx=10, pady=10)
        header_frame.pack_propagate(False)
        
        title_label = tk.Label(header_frame, text="StartSmart IoT Dashboard", 
                              font=("Arial", 24, "bold"), fg="white", bg="#2c3e50")
        title_label.pack(side=tk.LEFT, padx=20, pady=20)
        
        # Status indicator
        self.connection_status = tk.Label(header_frame, text="● CONNECTING", 
                                         font=("Arial", 12, "bold"), fg="#f39c12", bg="#2c3e50")
        self.connection_status.pack(side=tk.RIGHT, padx=20, pady=20)
        
        # Main content area
        main_frame = tk.Frame(self.root, bg="#1a1a1a")
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # Left panel - Hardware simulation
        left_panel = tk.Frame(main_frame, bg="#34495e", relief=tk.RAISED, bd=2)
        left_panel.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))
        
        # Hardware simulation title
        hw_title = tk.Label(left_panel, text="Hardware Simulation Panel", 
                           font=("Arial", 16, "bold"), fg="white", bg="#34495e")
        hw_title.pack(pady=15)
        
        # LED Grid (simulating multiple hardware indicators)
        led_frame = tk.Frame(left_panel, bg="#34495e")
        led_frame.pack(pady=20)
        
        self.leds = {}
        led_labels = [
            ("System Status", "system"),
            ("Application Status", "application"),
            ("Document Status", "documents"),
            ("Agency Status", "agencies"),
            ("AI Analysis", "ai"),
            ("Admin Review", "admin")
        ]
        
        for i, (label, key) in enumerate(led_labels):
            row = i // 2
            col = i % 2
            
            led_container = tk.Frame(led_frame, bg="#34495e")
            led_container.grid(row=row, column=col, padx=20, pady=15)
            
            # LED circle
            canvas = tk.Canvas(led_container, width=80, height=80, bg="#34495e", highlightthickness=0)
            canvas.pack()
            
            circle = canvas.create_oval(10, 10, 70, 70, fill="gray", outline="white", width=2)
            text = canvas.create_text(40, 40, text="OFF", font=("Arial", 8, "bold"), fill="white")
            
            # Label
            tk.Label(led_container, text=label, font=("Arial", 10), 
                    fg="white", bg="#34495e").pack(pady=5)
            
            self.leds[key] = {"canvas": canvas, "circle": circle, "text": text}
        
        # Right panel - Data display
        right_panel = tk.Frame(main_frame, bg="#34495e", relief=tk.RAISED, bd=2)
        right_panel.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(5, 0))
        
        # Data display title
        data_title = tk.Label(right_panel, text="Real-time Data Monitor", 
                             font=("Arial", 16, "bold"), fg="white", bg="#34495e")
        data_title.pack(pady=15)
        
        # Statistics display
        stats_frame = tk.Frame(right_panel, bg="#34495e")
        stats_frame.pack(fill=tk.X, padx=20, pady=10)
        
        self.stats_labels = {}
        stats_items = [
            ("Total Applications", "total_apps"),
            ("Pending Approvals", "pending_apps"),
            ("Approved Today", "approved_apps"),
            ("Active Agencies", "active_agencies"),
            ("Documents Processed", "documents_count"),
            ("System Uptime", "uptime")
        ]
        
        for i, (label, key) in enumerate(stats_items):
            row = i // 2
            col = i % 2
            
            stat_frame = tk.Frame(stats_frame, bg="#2c3e50", relief=tk.RAISED, bd=1)
            stat_frame.grid(row=row, column=col, padx=10, pady=5, sticky="ew")
            
            tk.Label(stat_frame, text=label, font=("Arial", 10), 
                    fg="#bdc3c7", bg="#2c3e50").pack(pady=2)
            
            value_label = tk.Label(stat_frame, text="0", font=("Arial", 14, "bold"), 
                                  fg="white", bg="#2c3e50")
            value_label.pack(pady=2)
            
            self.stats_labels[key] = value_label
        
        # Configure grid weights
        stats_frame.grid_columnconfigure(0, weight=1)
        stats_frame.grid_columnconfigure(1, weight=1)
        
        # Recent activity log
        log_frame = tk.Frame(right_panel, bg="#34495e")
        log_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        tk.Label(log_frame, text="Recent Activity Log", font=("Arial", 12, "bold"), 
                fg="white", bg="#34495e").pack(anchor=tk.W)
        
        # Log text area with scrollbar
        log_container = tk.Frame(log_frame, bg="#34495e")
        log_container.pack(fill=tk.BOTH, expand=True, pady=10)
        
        scrollbar = tk.Scrollbar(log_container)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        self.log_text = tk.Text(log_container, bg="#2c3e50", fg="#27ae60", 
                               font=("Courier", 9), yscrollcommand=scrollbar.set)
        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        scrollbar.config(command=self.log_text.yview)
        
        # Control panel
        control_frame = tk.Frame(self.root, bg="#2c3e50")
        control_frame.pack(fill=tk.X, padx=10, pady=10)
        
        tk.Button(control_frame, text="Refresh Data", command=self.manual_refresh,
                 bg="#3498db", fg="white", font=("Arial", 10), relief=tk.FLAT).pack(side=tk.LEFT, padx=5)
        
        tk.Button(control_frame, text="Clear Log", command=self.clear_log,
                 bg="#e67e22", fg="white", font=("Arial", 10), relief=tk.FLAT).pack(side=tk.LEFT, padx=5)
        
        tk.Button(control_frame, text="Exit", command=self.on_closing,
                 bg="#e74c3c", fg="white", font=("Arial", 10), relief=tk.FLAT).pack(side=tk.RIGHT, padx=5)
    
    def update_led(self, led_key, status, text=""):
        """Update LED indicator"""
        if led_key not in self.leds:
            return
        
        led = self.leds[led_key]
        
        if status == "active" or status == "approved":
            color = "#27ae60"  # Green
            display_text = text or "ON"
        elif status == "pending" or status == "warning":
            color = "#f39c12"  # Yellow
            display_text = text or "WAIT"
        elif status == "error" or status == "rejected":
            color = "#e74c3c"  # Red
            display_text = text or "ERR"
        else:
            color = "gray"
            display_text = text or "OFF"
        
        led["canvas"].itemconfig(led["circle"], fill=color)
        led["canvas"].itemconfig(led["text"], text=display_text)
    
    def log_activity(self, message):
        """Add message to activity log"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        
        # Keep only last 100 lines
        lines = self.log_text.get("1.0", tk.END).split('\n')
        if len(lines) > 100:
            self.log_text.delete("1.0", f"{len(lines)-100}.0")
    
    def fetch_system_data(self):
        """Fetch data from various API endpoints"""
        try:
            # Check system status
            response = requests.get(f"{self.api_base}/agencies/list", timeout=5)
            if response.status_code == 200:
                self.connection_status.config(text="● CONNECTED", fg="#27ae60")
                self.update_led("system", "active", "OK")
                
                # Get applications
                app_response = requests.get(f"{self.api_base}/applications/", timeout=5)
                if app_response.status_code == 200:
                    applications = app_response.json()
                    
                    total_apps = len(applications)
                    pending_apps = len([app for app in applications if app['status'] == 'submitted'])
                    approved_apps = len([app for app in applications if app['status'] == 'approved'])
                    
                    # Update statistics
                    self.stats_labels["total_apps"].config(text=str(total_apps))
                    self.stats_labels["pending_apps"].config(text=str(pending_apps))
                    self.stats_labels["approved_apps"].config(text=str(approved_apps))
                    self.stats_labels["active_agencies"].config(text="6")
                    self.stats_labels["documents_count"].config(text=str(total_apps * 3))
                    
                    # Update LEDs based on data
                    if total_apps > 0:
                        self.update_led("application", "active", str(total_apps))
                        self.update_led("documents", "active" if total_apps > 0 else "off")
                        self.update_led("agencies", "active", "6")
                        self.update_led("ai", "active", "AI")
                        self.update_led("admin", "pending" if pending_apps > 0 else "active")
                    
                    return True
                
            return False
            
        except requests.exceptions.RequestException:
            self.connection_status.config(text="● DISCONNECTED", fg="#e74c3c")
            self.update_led("system", "error", "ERR")
            return False
    
    def monitor_loop(self):
        """Main monitoring loop"""
        while self.monitoring:
            try:
                if self.fetch_system_data():
                    self.log_activity("System data updated successfully")
                else:
                    self.log_activity("Failed to fetch system data")
                
                time.sleep(5)  # Update every 5 seconds
                
            except Exception as e:
                self.log_activity(f"Monitoring error: {str(e)}")
                time.sleep(10)
    
    def start_monitoring(self):
        """Start the monitoring thread"""
        monitor_thread = threading.Thread(target=self.monitor_loop, daemon=True)
        monitor_thread.start()
        
        self.log_activity("IoT Dashboard started")
        self.log_activity("Connecting to StartSmart API...")
    
    def manual_refresh(self):
        """Manual refresh button handler"""
        self.log_activity("Manual refresh triggered")
        self.fetch_system_data()
    
    def clear_log(self):
        """Clear the activity log"""
        self.log_text.delete("1.0", tk.END)
        self.log_activity("Activity log cleared")
    
    def on_closing(self):
        """Handle window closing"""
        self.monitoring = False
        self.root.destroy()
    
    def run(self):
        """Start the dashboard"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.mainloop()

if __name__ == "__main__":
    print("Starting StartSmart IoT Dashboard...")
    print("This simulates IoT hardware integration for the approval system")
    
    dashboard = IoTDashboard()
    dashboard.run()
