from flask import Blueprint, jsonify, request
from database.mongo_connect import db
import datetime

iot_status = Blueprint("iot_status", __name__)

@iot_status.route("/status", methods=["GET"])
def get_status():
    """Get latest application status for IoT devices"""
    latest = db.applications.find_one(sort=[("_id", -1)])
    if latest:
        return jsonify({
            "application_name": latest.get("business_name", "N/A"),
            "application_number": latest.get("application_number", "N/A"),
            "status": latest.get("status", "Pending"),
            "applicant": latest.get("applicant_name", "N/A"),
            "business_type": latest.get("business_type", "N/A"),
            "created_at": latest.get("created_at"),
            "updated_at": latest.get("updated_at")
        })
    else:
        return jsonify({
            "application_name": "No Applications",
            "status": "No Data",
            "message": "No applications found in system"
        })

@iot_status.route("/system-health", methods=["GET"])
def get_system_health():
    """Get overall system health for IoT monitoring"""
    try:
        # Get application statistics
        total_apps = db.applications.count_documents({})
        pending_apps = db.applications.count_documents({"status": "submitted"})
        approved_apps = db.applications.count_documents({"status": "approved"})
        rejected_apps = db.applications.count_documents({"status": "rejected"})

        # Get recent activity (last 24 hours)
        yesterday = datetime.datetime.now() - datetime.timedelta(days=1)
        recent_apps = db.applications.count_documents({
            "created_at": {"$gte": yesterday}
        })

        # Calculate system health score
        if total_apps > 0:
            approval_rate = approved_apps / total_apps
            health_score = min(100, int((approval_rate * 70) + (recent_apps * 10) + 20))
        else:
            health_score = 50

        return jsonify({
            "system_status": "online",
            "health_score": health_score,
            "statistics": {
                "total_applications": total_apps,
                "pending_applications": pending_apps,
                "approved_applications": approved_apps,
                "rejected_applications": rejected_apps,
                "recent_applications_24h": recent_apps
            },
            "services": {
                "database": "online",
                "api": "online",
                "ai_analysis": "online",
                "document_processing": "online",
                "agency_integration": "online"
            },
            "timestamp": datetime.datetime.now().isoformat()
        })

    except Exception as e:
        return jsonify({
            "system_status": "error",
            "health_score": 0,
            "error": str(e),
            "timestamp": datetime.datetime.now().isoformat()
        }), 500

@iot_status.route("/alerts", methods=["GET"])
def get_alerts():
    """Get system alerts for IoT monitoring"""
    alerts = []

    try:
        # Check for pending applications that are overdue
        overdue_threshold = datetime.datetime.now() - datetime.timedelta(days=7)
        overdue_apps = list(db.applications.find({
            "status": "submitted",
            "created_at": {"$lt": overdue_threshold}
        }))

        for app in overdue_apps:
            alerts.append({
                "type": "warning",
                "message": f"Application {app.get('application_number', 'Unknown')} is overdue for review",
                "application_id": str(app["_id"]),
                "severity": "medium",
                "timestamp": datetime.datetime.now().isoformat()
            })

        # Check for high volume of applications
        today = datetime.datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        today_apps = db.applications.count_documents({
            "created_at": {"$gte": today}
        })

        if today_apps > 10:
            alerts.append({
                "type": "info",
                "message": f"High application volume today: {today_apps} applications",
                "severity": "low",
                "timestamp": datetime.datetime.now().isoformat()
            })

        # Check for system errors (simulated)
        if len(alerts) == 0:
            alerts.append({
                "type": "success",
                "message": "All systems operating normally",
                "severity": "info",
                "timestamp": datetime.datetime.now().isoformat()
            })

        return jsonify({
            "alerts": alerts,
            "alert_count": len([a for a in alerts if a["type"] in ["warning", "error"]]),
            "timestamp": datetime.datetime.now().isoformat()
        })

    except Exception as e:
        return jsonify({
            "alerts": [{
                "type": "error",
                "message": f"Failed to fetch alerts: {str(e)}",
                "severity": "high",
                "timestamp": datetime.datetime.now().isoformat()
            }],
            "alert_count": 1,
            "timestamp": datetime.datetime.now().isoformat()
        }), 500

@iot_status.route("/device-status", methods=["POST"])
def update_device_status():
    """Allow IoT devices to report their status"""
    data = request.json
    if not data:
        return jsonify({"error": "No data provided"}), 400

    device_id = data.get("device_id")
    status = data.get("status")

    if not device_id or not status:
        return jsonify({"error": "device_id and status required"}), 400

    # Store device status (in a real system, this would go to a devices collection)
    device_status = {
        "device_id": device_id,
        "status": status,
        "last_seen": datetime.datetime.now(),
        "metadata": data.get("metadata", {})
    }

    # For demo purposes, we'll just return success
    return jsonify({
        "message": "Device status updated",
        "device_id": device_id,
        "status": status,
        "timestamp": datetime.datetime.now().isoformat()
    })

@iot_status.route("/hardware-simulation", methods=["GET"])
def get_hardware_simulation_data():
    """Get data specifically for hardware simulation"""
    try:
        # Get latest application for LED status
        latest_app = db.applications.find_one(sort=[("_id", -1)])

        # Get system statistics
        total_apps = db.applications.count_documents({})
        pending_apps = db.applications.count_documents({"status": "submitted"})
        approved_apps = db.applications.count_documents({"status": "approved"})

        # Determine LED states
        led_states = {
            "power": "on",  # System is running
            "network": "on" if total_apps > 0 else "off",
            "processing": "blinking" if pending_apps > 0 else "off",
            "approval": "on" if approved_apps > 0 else "off",
            "error": "off"  # No errors currently
        }

        # Generate hardware metrics
        hardware_metrics = {
            "cpu_usage": min(100, 20 + (total_apps * 2)),
            "memory_usage": min(100, 15 + (total_apps * 1.5)),
            "network_activity": "high" if pending_apps > 5 else "normal",
            "temperature": 45 + (total_apps * 0.5),  # Simulated temperature
            "uptime_hours": 24  # Simulated uptime
        }

        return jsonify({
            "led_states": led_states,
            "hardware_metrics": hardware_metrics,
            "latest_application": {
                "name": latest_app.get("business_name", "No Applications") if latest_app else "No Applications",
                "status": latest_app.get("status", "none") if latest_app else "none",
                "type": latest_app.get("business_type", "N/A") if latest_app else "N/A"
            },
            "system_summary": {
                "total_applications": total_apps,
                "pending_applications": pending_apps,
                "approved_applications": approved_apps,
                "system_load": "normal" if total_apps < 20 else "high"
            },
            "timestamp": datetime.datetime.now().isoformat()
        })

    except Exception as e:
        return jsonify({
            "error": str(e),
            "led_states": {"error": "on"},
            "timestamp": datetime.datetime.now().isoformat()
        }), 500
