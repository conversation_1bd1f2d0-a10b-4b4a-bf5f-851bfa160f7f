from flask import Blueprint, request, jsonify
from database.mongo_connect import db
import os
import datetime
import random

ai_analysis = Blueprint("ai_analysis", __name__)

# Simulated AI analysis since we don't have OpenAI API key
def simulate_ai_analysis(business_type, location, description, business_name=None):
    """Simulate AI analysis for business approval requirements"""

    # Business type specific analysis
    analysis_templates = {
        "retail": {
            "risk_level": "Low",
            "required_approvals": ["Trade License", "GST Registration", "Shop & Establishment"],
            "estimated_timeline": "2-3 weeks",
            "success_probability": 0.85,
            "recommendations": [
                "Ensure proper inventory management system",
                "Maintain clear financial records",
                "Comply with local zoning regulations"
            ]
        },
        "manufacturing": {
            "risk_level": "High",
            "required_approvals": ["Trade License", "GST Registration", "Fire Safety", "Pollution Control", "Labor License"],
            "estimated_timeline": "6-8 weeks",
            "success_probability": 0.70,
            "recommendations": [
                "Prepare comprehensive environmental impact assessment",
                "Install proper safety equipment",
                "Ensure compliance with labor laws"
            ]
        },
        "restaurant": {
            "risk_level": "Medium",
            "required_approvals": ["Trade License", "GST Registration", "Fire Safety", "Food Safety License"],
            "estimated_timeline": "3-4 weeks",
            "success_probability": 0.80,
            "recommendations": [
                "Maintain high hygiene standards",
                "Install proper kitchen ventilation",
                "Train staff on food safety protocols"
            ]
        },
        "tech": {
            "risk_level": "Low",
            "required_approvals": ["Trade License", "GST Registration", "Shop & Establishment"],
            "estimated_timeline": "2-3 weeks",
            "success_probability": 0.90,
            "recommendations": [
                "Ensure data privacy compliance",
                "Maintain proper IT infrastructure",
                "Consider intellectual property protection"
            ]
        }
    }

    template = analysis_templates.get(business_type.lower(), analysis_templates["retail"])

    # Location-specific adjustments
    location_factors = {
        "mumbai": {"complexity_multiplier": 1.2, "additional_requirements": ["Municipal Corporation NOC"]},
        "delhi": {"complexity_multiplier": 1.3, "additional_requirements": ["Delhi Pollution Control Committee NOC"]},
        "bangalore": {"complexity_multiplier": 1.1, "additional_requirements": ["BBMP Approval"]},
        "chennai": {"complexity_multiplier": 1.0, "additional_requirements": ["Corporation Approval"]},
        "hyderabad": {"complexity_multiplier": 1.0, "additional_requirements": ["GHMC Approval"]}
    }

    location_factor = location_factors.get(location.lower(), {"complexity_multiplier": 1.0, "additional_requirements": []})

    # Generate analysis
    analysis = {
        "business_name": business_name or "Your Business",
        "business_type": business_type,
        "location": location,
        "risk_assessment": {
            "level": template["risk_level"],
            "score": random.uniform(0.3, 0.9),
            "factors": [
                f"Business type: {business_type}",
                f"Location: {location}",
                "Market conditions",
                "Regulatory environment"
            ]
        },
        "required_approvals": template["required_approvals"] + location_factor["additional_requirements"],
        "timeline_estimate": template["estimated_timeline"],
        "success_probability": min(template["success_probability"] / location_factor["complexity_multiplier"], 0.95),
        "recommendations": template["recommendations"],
        "next_steps": [
            "Prepare required documents",
            "Submit applications to relevant agencies",
            "Schedule inspections if required",
            "Follow up on application status"
        ],
        "estimated_costs": {
            "government_fees": random.randint(5000, 25000),
            "professional_fees": random.randint(10000, 50000),
            "total_estimated": random.randint(15000, 75000)
        }
    }

    return analysis

@ai_analysis.route("/", methods=["POST"])
def analyze():
    """Analyze business for approval requirements"""
    # Validate request data
    if not request.is_json:
        return jsonify({"error": "Request must be JSON"}), 400

    data = request.json
    if not data:
        return jsonify({"error": "No data provided"}), 400

    required_fields = ['business_type', 'location', 'description']
    for field in required_fields:
        if field not in data:
            return jsonify({"error": f"Missing required field: {field}"}), 400

    try:
        # Generate AI analysis
        analysis = simulate_ai_analysis(
            data['business_type'],
            data['location'],
            data['description'],
            data.get('business_name', None)
        )

        # Store analysis in database if application_id is provided
        if 'application_id' in data:
            analysis_record = {
                "application_id": data['application_id'],
                "analysis": analysis,
                "created_at": datetime.datetime.utcnow()
            }
            db.ai_analyses.insert_one(analysis_record)

        return jsonify({
            "success": True,
            "analysis": analysis
        })

    except Exception as e:
        return jsonify({"error": f"Analysis failed: {str(e)}"}), 500

@ai_analysis.route("/history/<application_id>", methods=["GET"])
def get_analysis_history(application_id):
    """Get AI analysis history for an application"""
    analyses = list(db.ai_analyses.find({"application_id": application_id}).sort("created_at", -1))

    for analysis in analyses:
        analysis["_id"] = str(analysis["_id"])

    return jsonify(analyses)

@ai_analysis.route("/document-verification", methods=["POST"])
def verify_document():
    """AI-powered document verification simulation"""
    data = request.json
    if not data:
        return jsonify({"error": "No data provided"}), 400

    if "document_type" not in data:
        return jsonify({"error": "Missing document_type"}), 400

    # Simulate document verification
    verification_results = {
        "business_plan": {
            "verified": True,
            "confidence": 0.92,
            "issues": [],
            "suggestions": ["Add more detailed financial projections", "Include market research data"]
        },
        "identity_proof": {
            "verified": True,
            "confidence": 0.98,
            "issues": [],
            "suggestions": []
        },
        "address_proof": {
            "verified": True,
            "confidence": 0.95,
            "issues": [],
            "suggestions": ["Ensure document is recent (within 3 months)"]
        }
    }

    doc_type = data["document_type"].lower().replace(" ", "_")
    result = verification_results.get(doc_type, {
        "verified": True,
        "confidence": 0.85,
        "issues": [],
        "suggestions": ["Document appears valid"]
    })

    return jsonify({
        "document_type": data["document_type"],
        "verification_result": result,
        "verified_at": datetime.datetime.now().isoformat()
    })